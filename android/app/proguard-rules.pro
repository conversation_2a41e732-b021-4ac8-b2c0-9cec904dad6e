# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# react-native-reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }

# React Native Core
-keep class com.facebook.react.** { *; }
-keep class com.facebook.hermes.** { *; }

# SQLite and Database
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }

# Expo modules
-keep class expo.modules.** { *; }
-keep class versioned.host.exp.exponent.** { *; }

# React Native Google Mobile Ads
-keep class com.google.android.gms.ads.** { *; }

# Sentry
-keep class io.sentry.** { *; }

# Keep JavaScript interface methods
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep native methods
-keepclassmembers class * {
    native <methods>;
}

# Add any project specific keep options here:
