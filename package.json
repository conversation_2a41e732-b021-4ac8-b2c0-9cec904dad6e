{"name": "odia-calendar-lite", "main": "src/index.js", "version": "1.0.2", "scripts": {"start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "android": "expo run:android", "ios": "expo run:ios", "version:check": "node scripts/update-version.js", "version:update": "node scripts/update-version.js"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.0.0", "@sentry/react-native": "~6.10.0", "@supabase/supabase-js": "^2.49.4", "expo": "~52.0.36", "expo-asset": "^11.0.5", "expo-background-task": "~0.1.4", "expo-constants": "~17.0.7", "expo-dev-client": "~5.0.20", "expo-font": "~13.0.4", "expo-linear-gradient": "^14.0.1", "expo-linking": "~7.0.3", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.22", "expo-sqlite": "^15.1.4", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.6", "expo-task-manager": "~12.0.6", "expo-updates": "~0.27.4", "lucide-react-native": "^0.475.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-google-mobile-ads": "^14.1.0", "react-native-reanimated": "^3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "zustand": "^5.0.2", "expo-store-review": "~8.0.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@react-native-community/cli": "^18.0.0", "@types/react": "~18.3.12", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-remove-console": "^6.9.4", "typescript": "~5.8.2"}, "private": true}