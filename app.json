{"expo": {"name": "Odia Calendar", "slug": "odia-calendar-app", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/icon.png", "scheme": "odiacalendar", "userInterfaceStyle": "automatic", "newArchEnabled": true, "jsEngine": "hermes", "owner": "gyana2010", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.odiacalendar.app", "appStoreUrl": "https://apps.apple.com/app/odia-calendar/id6738234567"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.kalingatech.odia.simplecalendar", "versionCode": 18, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"], "config": {"googleMobileAdsAppId": "ca-app-pub-7418777810665263~3652552932"}, "playStoreUrl": "https://play.google.com/store/apps/details?id=com.kalingatech.odia.simplecalendar"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-router", {"origin": "https://kalingatech.com/"}], ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-7418777810665263~3652552932", "iosAppId": "ca-app-pub-7418777810665263~5096646482", "userTrackingPermission": "This app shows personalized ads to support free access to Odia calendar features."}], ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "react-native", "organization": "kalinga-tech"}], "expo-splash-screen", "expo-background-task"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": "https://kalingatech.com/"}, "eas": {"projectId": "77561ab6-061b-4608-9bf7-4f7a01c3f6f4"}}, "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/77561ab6-061b-4608-9bf7-4f7a01c3f6f4"}}}