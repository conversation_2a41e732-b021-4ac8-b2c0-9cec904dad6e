import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity, Switch, Alert, ActivityIndicator } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { translations } from '@/constants/translations';
import { useReminderStore } from '@/store/reminder-store';
import { UserReminder, DEFAULT_SOUND_OPTIONS } from '@/types/reminders';
import NotificationPermissionAlert from '@/components/reminders/NotificationPermissionAlert';
import { notificationService } from '@/services/notification-service';
import { useAdMob } from '@/components/common/AdMobProvider';
import colors from '@/constants/colors';
import TithiPicker from '@/components/reminders/TithiPicker';
import { formatTime } from '../src/utils/date-formatter';
import { Clock, Bell } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { validateTithiCombination, isSpecialTithi } from '@/utils/tithi-calculator';
import { logger } from '@/utils/logging-sentry';

// Odia month options
const ODIA_MONTH_OPTIONS = [
  { value: 'ବୈଶାଖ', label: 'ବୈଶାଖ (Baishakha)' },
  { value: 'ଜ୍ୟେଷ୍ଠ', label: 'ଜ୍ୟେଷ୍ଠ (Jyestha)' },
  { value: 'ଆଷାଢ଼', label: 'ଆଷାଢ଼ (Ashadha)' },
  { value: 'ଶ୍ରାବଣ', label: 'ଶ୍ରାବଣ (Shravana)' },
  { value: 'ଭାଦ୍ରବ', label: 'ଭାଦ୍ରବ (Bhadrava)' },
  { value: 'ଆଶ୍ୱିନ', label: 'ଆଶ୍ୱିନ (Ashwina)' },
  { value: 'କାର୍ତ୍ତିକ', label: 'କାର୍ତ୍ତିକ (Kartika)' },
  { value: 'ମାର୍ଗଶିର', label: 'ମାର୍ଗଶିର (Margashira)' },
  { value: 'ପୌଷ', label: 'ପୌଷ (Pousha)' },
  { value: 'ମାଘ', label: 'ମାଘ (Magha)' },
  { value: 'ଫାଲ୍ଗୁନ', label: 'ଫାଲ୍ଗୁନ (Phalguna)' },
  { value: 'ଚୈତ୍ର', label: 'ଚୈତ୍ର (Chaitra)' },
];

// Paksha options
const PAKSHA_OPTIONS = [
  { value: 'ଶୁକ୍ଳ', label: 'ଶୁକ୍ଳ (Shukla)' },
  { value: 'କୃଷ୍ଣ', label: 'କୃଷ୍ଣ (Krishna)' },
];

// Tithi options
const TITHI_OPTIONS = [
  { value: 'ପ୍ରତିପଦା', label: 'ପ୍ରତିପଦା (Pratipada)' },
  { value: 'ଦ୍ୱିତୀୟା', label: 'ଦ୍ୱିତୀୟା (Dwitiya)' },
  { value: 'ତୃତୀୟା', label: 'ତୃତୀୟା (Tritiya)' },
  { value: 'ଚତୁର୍ଥୀ', label: 'ଚତୁର୍ଥୀ (Chaturthi)' },
  { value: 'ପଞ୍ଚମୀ', label: 'ପଞ୍ଚମୀ (Panchami)' },
  { value: 'ଷଷ୍ଠୀ', label: 'ଷଷ୍ଠୀ (Shashthi)' },
  { value: 'ସପ୍ତମୀ', label: 'ସପ୍ତମୀ (Saptami)' },
  { value: 'ଅଷ୍ଟମୀ', label: 'ଅଷ୍ଟମୀ (Ashtami)' },
  { value: 'ନବମୀ', label: 'ନବମୀ (Navami)' },
  { value: 'ଦଶମୀ', label: 'ଦଶମୀ (Dashami)' },
  { value: 'ଏକାଦଶୀ', label: 'ଏକାଦଶୀ (Ekadashi)' },
  { value: 'ଦ୍ୱାଦଶୀ', label: 'ଦ୍ୱାଦଶୀ (Dwadashi)' },
  { value: 'ତ୍ରୟୋଦଶୀ', label: 'ତ୍ରୟୋଦଶୀ (Trayodashi)' },
  { value: 'ଚତୁର୍ଦ୍ଦଶୀ', label: 'ଚତୁର୍ଦ୍ଦଶୀ (Chaturdashi)' },
  { value: 'ପୂର୍ଣ୍ଣିମା', label: 'ପୂର୍ଣ୍ଣିମା (Purnima)' },
  { value: 'ଅମାବାସ୍ୟା', label: 'ଅମାବାସ୍ୟା (Amavasya)' },
];

// Reminder type options - defined inside the component to access translations
const getReminderTypeOptions = (t: any) => [
  { value: 'monthly_tithi', label: t.monthlyTithi },
  { value: 'yearly_tithi', label: t.yearlyTithi },
];

// Sound options - defined inside the component to access translations
const getSoundOptions = () => DEFAULT_SOUND_OPTIONS.map(sound => ({
  value: sound.id,
  label: sound.name, // Sound names are not translated as they are system identifiers
}));

export default function AddReminderScreen() {
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const t = translations[language];
  const theme = isDarkMode ? colors.dark : colors.light;
  const router = useRouter();
  const { showInterstitial } = useAdMob();

  const { selectedReminder, createReminder, updateReminder } = useReminderStore();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [reminderType, setReminderType] = useState<string>('monthly_tithi');
  const [odiaMonth, setOdiaMonth] = useState<string | undefined>(undefined);
  const [paksha, setPaksha] = useState<string | undefined>(undefined);
  const [tithi, setTithi] = useState<string | undefined>(undefined);
  const [isRecurring, setIsRecurring] = useState(true);
  const [notificationTime, setNotificationTime] = useState('00:00');
  const [soundName, setSoundName] = useState('default');
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [permissionGranted, setPermissionGranted] = useState<boolean | null>(null);

  // Initialize form with selected reminder if editing
  useEffect(() => {
    if (selectedReminder) {
      setTitle(selectedReminder.title);
      setDescription(selectedReminder.description || '');
      setReminderType(selectedReminder.reminderType);
      setOdiaMonth(selectedReminder.odiaMonth);
      setPaksha(selectedReminder.paksha);
      setTithi(selectedReminder.tithi);
      setIsRecurring(selectedReminder.isRecurring);
      setNotificationTime(selectedReminder.notificationTime);
      setSoundName(selectedReminder.soundName);
    }
  }, [selectedReminder]);

  // Check notification permissions when the screen loads
  useEffect(() => {
    const checkPermissions = async () => {
      const status = await notificationService.getPermissionStatus();
      setPermissionGranted(status?.granted || false);
    };

    checkPermissions();
  }, []);

  // Handle permission changes
  const handlePermissionChange = (granted: boolean) => {
    setPermissionGranted(granted);
  };

  // Handle time picker change
  const handleTimeChange = useCallback((_event: any, selectedDate?: Date) => {
    setShowTimePicker(false);

    if (selectedDate) {
      const hours = selectedDate.getHours().toString().padStart(2, '0');
      const minutes = selectedDate.getMinutes().toString().padStart(2, '0');
      setNotificationTime(`${hours}:${minutes}`);
    }
  }, []);

  // Function to save the reminder
  const saveReminder = async () => {
    // Validate that the combination of month, paksha, and tithi exists
    setIsValidating(true);
    try {
      const isValid = await validateTithiCombination(
        reminderType === 'yearly_tithi' ? odiaMonth : null,
        paksha,
        tithi
      );

      if (!isValid) {
        Alert.alert(
          t.validationError,
          t.invalidCombination
        );
        setIsValidating(false);
        return;
      }
    } catch (error) {
      console.error('Error validating tithi combination:', error);
      // Continue with saving even if validation fails due to technical issues
      // This is to avoid blocking users from creating reminders if there's a database issue
    } finally {
      setIsValidating(false);
    }

    // Create reminder object
    const reminder: UserReminder = {
      id: selectedReminder?.id,
      title,
      description: description.trim() || undefined,
      reminderType: reminderType as any,
      odiaMonth,
      paksha,
      tithi,
      isRecurring,
      recurrenceInterval: isRecurring ? 1 : undefined,
      notificationTime,
      soundName,
      isEnabled: true,
    };

    // Save reminder
    let success = false;

    if (selectedReminder) {
      // Update existing reminder
      success = await updateReminder(reminder);
    } else {
      // Create new reminder
      const newReminder = await createReminder(reminder);
      success = !!newReminder;
    }

    if (success) {
      // Navigate back immediately for instant user feedback
      router.back();

      // Show interstitial ad in background (non-blocking)
      showInterstitial('reminderCreation').catch(error => {
        logger.debug('Background interstitial ad failed', { error });
      });
    } else {
      Alert.alert(
        t.error,
        t.saveFailed
      );
    }
  };

  // Handle save button press
  const handleSave = useCallback(async () => {
    // Validate form
    if (!title.trim()) {
      Alert.alert(
        t.validationError,
        t.titleRequired
      );
      return;
    }

    // Check if tithi is required
    if (!tithi) {
      Alert.alert(
        t.validationError,
        t.tithiRequired
      );
      return;
    }

    // Check if paksha is required (not required for special tithis like Amavasya/Purnima)
    const isSpecial = isSpecialTithi(tithi);
    if (!isSpecial && !paksha) {
      Alert.alert(
        t.validationError,
        t.pakshaRequired
      );
      return;
    }

    if (reminderType === 'yearly_tithi' && !odiaMonth) {
      Alert.alert(
        t.validationError,
        t.monthRequired
      );
      return;
    }

    // Check notification permissions
    const permissionStatus = await notificationService.getPermissionStatus();
    if (!permissionStatus?.granted) {
      // Show a warning but allow the user to continue
      Alert.alert(
        t.notificationPermissionRequired,
        t.notificationPermissionMessage,
        [
          {
            text: t.openSettings,
            onPress: async () => {
              await notificationService.openNotificationSettings();
            },
          },
          {
            text: t.later,
            style: 'cancel',
          },
          {
            text: t.save,
            onPress: async () => await saveReminder(),
          },
        ]
      );
      return;
    }

    // If permissions are granted, save directly
    await saveReminder();
  }, [
    title, description, reminderType, odiaMonth, paksha, tithi,
    isRecurring, notificationTime, soundName, selectedReminder, t, showInterstitial
  ]);

  return (
    <>
      <Stack.Screen
        options={{
          title: selectedReminder
            ? t.editReminder
            : t.addReminder,
          headerRight: () => (
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSave}
              disabled={isValidating}
            >
              {isValidating ? (
                <ActivityIndicator size="small" color={theme.primary} />
              ) : (
                <Text style={[styles.saveButtonText, { color: theme.primary }]}>
                  {t.save}
                </Text>
              )}
            </TouchableOpacity>
          ),
        }}
      />

      {/* Show permission alert if permissions are not granted */}
      {permissionGranted === false && (
        <NotificationPermissionAlert
          onPermissionChange={handlePermissionChange}
          showOnlyIfDenied={true}
        />
      )}

      <ScrollView
        style={[styles.container, { backgroundColor: theme.background }]}
        contentContainerStyle={styles.content}
      >
        {/* Title */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.text }]}>
            {t.title}*
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                backgroundColor: theme.inputBackground,
                color: theme.text,
                borderColor: theme.border
              }
            ]}
            value={title}
            onChangeText={setTitle}
            placeholder={t.titlePlaceholder}
            placeholderTextColor={theme.textSecondary}
          />
        </View>

        {/* Description */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.text }]}>
            {t.description}
          </Text>
          <TextInput
            style={[
              styles.input,
              styles.textArea,
              {
                backgroundColor: theme.inputBackground,
                color: theme.text,
                borderColor: theme.border
              }
            ]}
            value={description}
            onChangeText={setDescription}
            placeholder={t.descriptionPlaceholder}
            placeholderTextColor={theme.textSecondary}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Reminder Type */}
        <TithiPicker
          label={t.reminderType}
          value={reminderType}
          options={getReminderTypeOptions(t)}
          onValueChange={setReminderType}
        />

        {/* Odia Month (for yearly reminders) */}
        {reminderType === 'yearly_tithi' && (
          <TithiPicker
            label={t.reminderOdiaMonth}
            value={odiaMonth}
            options={ODIA_MONTH_OPTIONS}
            onValueChange={setOdiaMonth}
            placeholder={t.selectMonth}
          />
        )}

        {/* Paksha */}
        <TithiPicker
          label={t.reminderPaksha}
          value={paksha}
          options={PAKSHA_OPTIONS}
          onValueChange={setPaksha}
          placeholder={t.selectPaksha}
        />

        {/* Tithi */}
        <TithiPicker
          label={t.reminderTithi}
          value={tithi}
          options={TITHI_OPTIONS}
          onValueChange={setTithi}
          placeholder={t.selectTithi}
        />

        {/* Notification Time */}
        <View style={styles.formGroup}>
          <Text style={[styles.label, { color: theme.text }]}>
            {t.notificationTime}
          </Text>
          <TouchableOpacity
            style={[
              styles.timeButton,
              {
                backgroundColor: theme.inputBackground,
                borderColor: theme.border
              }
            ]}
            onPress={() => setShowTimePicker(true)}
          >
            <Clock size={20} color={theme.textSecondary} />
            <Text style={[styles.timeText, { color: theme.text }]}>
              {formatTime(notificationTime)}
            </Text>
          </TouchableOpacity>

          {showTimePicker && (
            <DateTimePicker
              value={(() => {
                const [hours, minutes] = notificationTime.split(':').map(Number);
                const date = new Date();
                date.setHours(hours, minutes, 0, 0);
                return date;
              })()}
              mode="time"
              is24Hour={false}
              display="default"
              onChange={handleTimeChange}
            />
          )}
        </View>

        {/* Sound */}
        <TithiPicker
          label={t.sound}
          value={soundName}
          options={getSoundOptions()}
          onValueChange={setSoundName}
        />

        {/* Recurring */}
        <View style={styles.switchContainer}>
          <View style={styles.switchLabel}>
            <Bell size={20} color={theme.text} />
            <Text style={[styles.switchText, { color: theme.text }]}>
              {t.recurring}
            </Text>
          </View>
          <Switch
            value={isRecurring}
            onValueChange={setIsRecurring}
            trackColor={{ false: theme.border, true: theme.primary }}
            thumbColor={theme.background}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    paddingTop: 12,
    paddingBottom: 12,
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
  },
  timeText: {
    fontSize: 16,
    marginLeft: 8,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchText: {
    fontSize: 16,
    marginLeft: 8,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
