import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Calendar, Heart, Home, ChevronDown, ChevronUp } from 'lucide-react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { useCalendarStore } from '@/store/calendar-store';
import { formatDateWithLocalDigits } from '@/utils/language-utils';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { useFestivals, useMarriageDates, useBrataGharaDates } from '@/hooks/useCalendarData';
import { PanchangData } from '@/types/database';

/**
 * A unified component that displays festivals, marriage dates, and brata ghara dates
 * in a vertical list with collapsible sections
 */
const EventsList: React.FC = () => {
  // State for collapsible sections
  const [festivalExpanded, setFestivalExpanded] = useState(true);
  const [marriageExpanded, setMarriageExpanded] = useState(true);
  const [brataGharaExpanded, setBrataGharaExpanded] = useState(true);
  
  // Get data from hooks
  const { selectedYear, selectedMonth } = useCalendarStore();
  const { festivals, loading: festivalsLoading } = useFestivals(selectedYear, selectedMonth);
  const { marriageDates, loading: marriageDatesLoading } = useMarriageDates(selectedYear, selectedMonth);
  const { brataGharaDates, loading: brataGharaDatesLoading } = useBrataGharaDates(selectedYear, selectedMonth);
  
  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;
  
  // Format date for display - memoized to prevent recreation on each render
  const formatDate = useCallback((dateStr: string) => {
    const date = new Date(dateStr);
    return formatDateWithLocalDigits(date, language, {
      day: 'numeric',
      month: 'short',
    });
  }, [language]);

  // Section header component with toggle
  const SectionHeader = ({ 
    title, 
    icon, 
    color, 
    expanded, 
    onToggle, 
    count 
  }: {
    title: string;
    icon: React.ReactNode;
    color: string;
    expanded: boolean;
    onToggle: () => void;
    count: number;
  }) => (
    <TouchableOpacity 
      style={styles.sectionHeader} 
      onPress={onToggle}
      activeOpacity={0.7}
    >
      <View style={styles.sectionHeaderLeft}>
        {icon}
        <Text style={[styles.sectionHeaderText, { color: theme.text }]}>
          {title}
        </Text>
        <View style={[styles.countBadge, { backgroundColor: color }]}>
          <Text style={styles.countText}>{count}</Text>
        </View>
      </View>
      {expanded ? 
        <ChevronUp size={20} color={theme.text} /> : 
        <ChevronDown size={20} color={theme.text} />
      }
    </TouchableOpacity>
  );

  // Festival Item Component
  const FestivalItem = useCallback(({ item }: { item: PanchangData }) => {
    const formattedDate = formatDate(item.eng_date);
    const festivalNames = item.festivals.split(',').map(f => f.trim());
    
    return (
      <View style={[styles.eventItem, { backgroundColor: isDarkMode ? theme.card : theme.festival + '10' }]}>
        <View style={[styles.dateBadge, { backgroundColor: theme.festival }]}>
          <Text style={styles.dateText}>{formattedDate}</Text>
        </View>
        <View style={styles.eventContent}>
          {festivalNames.map((festival, index) => (
            <Text 
              key={index}
              style={[styles.eventName, { color: theme.text }]}
              numberOfLines={2}
            >
              {festival}
            </Text>
          ))}
        </View>
      </View>
    );
  }, [formatDate, theme, isDarkMode]);

  // Marriage Date Item Component
  const MarriageItem = useCallback(({ item }: { item: PanchangData }) => {
    const formattedDate = formatDate(item.eng_date);
    const muhurtaLabel = language === 'or' ? 'ମୁହୂର୍ତ୍ତ' : 'Muhurta';
    const muhurta = item.additional || (language === 'or' ? 'ଶୁଭ ମୁହୂର୍ତ୍ତ' : 'Auspicious Time');
    
    return (
      <View style={[styles.eventItem, { backgroundColor: isDarkMode ? theme.card : theme.marriage + '10' }]}>
        <View style={[styles.dateBadge, { backgroundColor: theme.marriage }]}>
          <Text style={styles.dateText}>{formattedDate}</Text>
        </View>
        <View style={styles.eventContent}>
          <Text style={[styles.eventLabel, { color: theme.subtext }]}>
            {muhurtaLabel}
          </Text>
          <Text 
            style={[styles.eventName, { color: theme.text }]}
            numberOfLines={2}
          >
            {muhurta}
          </Text>
        </View>
      </View>
    );
  }, [formatDate, theme, language, isDarkMode]);

  // Brata Ghara Item Component
  const BrataGharaItem = useCallback(({ item }: { item: PanchangData }) => {
    const formattedDate = formatDate(item.eng_date);
    const muhurtaLabel = language === 'or' ? 'ମୁହୂର୍ତ୍ତ' : 'Muhurta';
    const muhurta = item.additional || (language === 'or' ? 'ଶୁଭ ମୁହୂର୍ତ୍ତ' : 'Auspicious Time');
    
    return (
      <View style={[styles.eventItem, { backgroundColor: isDarkMode ? theme.card : theme.brataGhara + '10' }]}>
        <View style={[styles.dateBadge, { backgroundColor: theme.brataGhara }]}>
          <Text style={styles.dateText}>{formattedDate}</Text>
        </View>
        <View style={styles.eventContent}>
          <Text style={[styles.eventLabel, { color: theme.subtext }]}>
            {muhurtaLabel}
          </Text>
          <Text 
            style={[styles.eventName, { color: theme.text }]}
            numberOfLines={2}
          >
            {muhurta}
          </Text>
        </View>
      </View>
    );
  }, [formatDate, theme, language, isDarkMode]);
  
  // Empty state component
  const EmptySection = useCallback(({ message }: { message: string }) => (
    <View style={[styles.emptyContainer, { 
      borderColor: theme.border,
      backgroundColor: isDarkMode ? 'transparent' : theme.card
    }]}>
      <Text style={[styles.emptyText, { color: theme.subtext }]}>
        {message}
      </Text>
    </View>
  ), [theme, isDarkMode]);

  return (
    <View style={styles.container}>
      {/* Festivals Section */}
      <SectionHeader
        title={translations[language].festivals}
        icon={<Calendar size={20} color={theme.festival} />}
        color={theme.festival}
        expanded={festivalExpanded}
        onToggle={() => setFestivalExpanded(!festivalExpanded)}
        count={festivals.length}
      />
      
      {festivalExpanded && (
        festivals.length > 0 ? (
          <FlatList
            data={festivals}
            keyExtractor={(item) => item.eng_date + item.festivals}
            renderItem={({ item }) => <FestivalItem item={item} />}
            scrollEnabled={false} // Disable scrolling within this list
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <EmptySection message={translations[language].noFestivals} />
        )
      )}
      
      {/* Marriage Dates Section */}
      <SectionHeader
        title={translations[language].marriageDates}
        icon={<Heart size={20} color={theme.marriage} />}
        color={theme.marriage}
        expanded={marriageExpanded}
        onToggle={() => setMarriageExpanded(!marriageExpanded)}
        count={marriageDates.length}
      />
      
      {marriageExpanded && (
        marriageDates.length > 0 ? (
          <FlatList
            data={marriageDates}
            keyExtractor={(item) => item.eng_date}
            renderItem={({ item }) => <MarriageItem item={item} />}
            scrollEnabled={false} // Disable scrolling within this list
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <EmptySection message={translations[language].noMarriageDates} />
        )
      )}
      
      {/* Brata Ghara Dates Section */}
      <SectionHeader
        title={translations[language].brataGharaDates}
        icon={<Home size={20} color={theme.brataGhara} />}
        color={theme.brataGhara}
        expanded={brataGharaExpanded}
        onToggle={() => setBrataGharaExpanded(!brataGharaExpanded)}
        count={brataGharaDates.length}
      />
      
      {brataGharaExpanded && (
        brataGharaDates.length > 0 ? (
          <FlatList
            data={brataGharaDates}
            keyExtractor={(item) => item.eng_date}
            renderItem={({ item }) => <BrataGharaItem item={item} />}
            scrollEnabled={false} // Disable scrolling within this list
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <EmptySection message={translations[language].noBrataGharaDates} />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 8,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionHeaderText: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  countBadge: {
    marginLeft: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  countText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  listContent: {
    paddingHorizontal: 16,
  },
  eventItem: {
    flexDirection: 'row',
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  dateBadge: {
    width: 60,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  dateText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  eventContent: {
    flex: 1,
    padding: 12,
  },
  eventLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  eventName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  emptyContainer: {
    marginHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 14,
  },
});

export default EventsList;
