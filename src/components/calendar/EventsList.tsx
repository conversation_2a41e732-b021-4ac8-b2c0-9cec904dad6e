import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Calendar, Heart, Home, ChevronDown, ChevronUp } from 'lucide-react-native';
import { useDarkMode, useLanguage } from '@/store/settings-store';
import { useCalendarStore } from '@/store/calendar-store';
import { formatDateWithLocalDigits } from '@/utils/language-utils';
import { translations } from '@/constants/translations';
import colors from '@/constants/colors';
import { useFestivals, useMarriageDates, useBrataGharaDates } from '@/hooks/useCalendarData';
import { PanchangData } from '@/types/database';

/**
 * A unified component that displays festivals, marriage dates, and brata ghara dates
 * in a vertical list with collapsible sections
 */
const EventsList: React.FC = () => {
  // State for collapsible sections
  const [festivalExpanded, setFestivalExpanded] = useState(true);
  const [marriageExpanded, setMarriageExpanded] = useState(true);
  const [brataGharaExpanded, setBrataGharaExpanded] = useState(true);

  // Get data from hooks
  const { selectedYear, selectedMonth } = useCalendarStore();
  const { festivals } = useFestivals(selectedYear, selectedMonth);
  const { marriageDates } = useMarriageDates(selectedYear, selectedMonth);
  const { brataGharaDates } = useBrataGharaDates(selectedYear, selectedMonth);

  const isDarkMode = useDarkMode();
  const language = useLanguage();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Format date for display - memoized to prevent recreation on each render
  const formatDate = useCallback((dateStr: string) => {
    const date = new Date(dateStr);
    return formatDateWithLocalDigits(date, language, {
      day: 'numeric',
      month: 'short',
    });
  }, [language]);

  // Section header component with toggle - Apple-style minimal
  const SectionHeader = ({
    title,
    icon,
    color,
    expanded,
    onToggle,
    count
  }: {
    title: string;
    icon: React.ReactNode;
    color: string;
    expanded: boolean;
    onToggle: () => void;
    count: number;
  }) => (
    <TouchableOpacity
      style={styles.sectionHeader}
      onPress={onToggle}
      activeOpacity={0.6}
    >
      <View style={styles.sectionHeaderLeft}>
        {icon}
        <Text style={[styles.sectionHeaderText, { color: theme.text }]}>
          {title}
        </Text>
        {count > 0 && (
          <View style={[styles.countBadge, { backgroundColor: color }]}>
            <Text style={styles.countText}>{count}</Text>
          </View>
        )}
      </View>
      {expanded ?
        <ChevronUp size={16} color={theme.subtext} /> :
        <ChevronDown size={16} color={theme.subtext} />
      }
    </TouchableOpacity>
  );

  // Festival Item Component - Apple-style clean design
  const FestivalItem = useCallback(({ item }: { item: PanchangData }) => {
    const formattedDate = formatDate(item.eng_date);
    const festivalNames = item.festivals.split(',').map(f => f.trim());

    return (
      <View style={styles.simpleEventItem}>
        <Text style={[styles.dateLabel, { color: theme.festival }]}>
          {formattedDate}
        </Text>
        <View style={styles.eventTextContainer}>
          {festivalNames.map((festival, index) => (
            <Text
              key={index}
              style={[styles.eventText, { color: theme.text }]}
              numberOfLines={1}
            >
              {festival}
            </Text>
          ))}
        </View>
      </View>
    );
  }, [formatDate, theme]);

  // Marriage Date Item Component - Apple-style clean design
  const MarriageItem = useCallback(({ item }: { item: PanchangData }) => {
    const formattedDate = formatDate(item.eng_date);
    const muhurta = item.additional || (language === 'or' ? 'ଶୁଭ ମୁହୂର୍ତ୍ତ' : 'Auspicious Time');

    return (
      <View style={styles.simpleEventItem}>
        <Text style={[styles.dateLabel, { color: theme.marriage }]}>
          {formattedDate}
        </Text>
        <View style={styles.eventTextContainer}>
          <Text
            style={[styles.eventText, { color: theme.text }]}
            numberOfLines={1}
          >
            {muhurta}
          </Text>
        </View>
      </View>
    );
  }, [formatDate, theme, language]);

  // Brata Ghara Item Component - Apple-style clean design
  const BrataGharaItem = useCallback(({ item }: { item: PanchangData }) => {
    const formattedDate = formatDate(item.eng_date);
    const muhurta = item.additional || (language === 'or' ? 'ଶୁଭ ମୁହୂର୍ତ୍ତ' : 'Auspicious Time');

    return (
      <View style={styles.simpleEventItem}>
        <Text style={[styles.dateLabel, { color: theme.brataGhara }]}>
          {formattedDate}
        </Text>
        <View style={styles.eventTextContainer}>
          <Text
            style={[styles.eventText, { color: theme.text }]}
            numberOfLines={1}
          >
            {muhurta}
          </Text>
        </View>
      </View>
    );
  }, [formatDate, theme, language]);

  // Empty state component - Apple-style minimal
  const EmptySection = useCallback(({ message }: { message: string }) => (
    <View style={styles.emptyContainer}>
      <Text style={[styles.emptyText, { color: theme.subtext }]}>
        {message}
      </Text>
    </View>
  ), [theme]);

  return (
    <View style={styles.container}>
      {/* Festivals Section */}
      <SectionHeader
        title={translations[language].festivals}
        icon={<Calendar size={20} color={theme.festival} />}
        color={theme.festival}
        expanded={festivalExpanded}
        onToggle={() => setFestivalExpanded(!festivalExpanded)}
        count={festivals.length}
      />

      {festivalExpanded && (
        festivals.length > 0 ? (
          <FlatList
            data={festivals}
            keyExtractor={(item) => item.eng_date + item.festivals}
            renderItem={({ item }) => <FestivalItem item={item} />}
            scrollEnabled={false} // Disable scrolling within this list
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <EmptySection message={translations[language].noFestivals} />
        )
      )}

      {/* Marriage Dates Section */}
      <SectionHeader
        title={translations[language].marriageDates}
        icon={<Heart size={20} color={theme.marriage} />}
        color={theme.marriage}
        expanded={marriageExpanded}
        onToggle={() => setMarriageExpanded(!marriageExpanded)}
        count={marriageDates.length}
      />

      {marriageExpanded && (
        marriageDates.length > 0 ? (
          <FlatList
            data={marriageDates}
            keyExtractor={(item) => item.eng_date}
            renderItem={({ item }) => <MarriageItem item={item} />}
            scrollEnabled={false} // Disable scrolling within this list
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <EmptySection message={translations[language].noMarriageDates} />
        )
      )}

      {/* Brata Ghara Dates Section */}
      <SectionHeader
        title={translations[language].brataGharaDates}
        icon={<Home size={20} color={theme.brataGhara} />}
        color={theme.brataGhara}
        expanded={brataGharaExpanded}
        onToggle={() => setBrataGharaExpanded(!brataGharaExpanded)}
        count={brataGharaDates.length}
      />

      {brataGharaExpanded && (
        brataGharaDates.length > 0 ? (
          <FlatList
            data={brataGharaDates}
            keyExtractor={(item) => item.eng_date}
            renderItem={({ item }) => <BrataGharaItem item={item} />}
            scrollEnabled={false} // Disable scrolling within this list
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <EmptySection message={translations[language].noBrataGharaDates} />
        )
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 4,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 6,
  },
  countBadge: {
    marginLeft: 6,
    paddingHorizontal: 6,
    paddingVertical: 1,
    borderRadius: 8,
    minWidth: 16,
    alignItems: 'center',
  },
  countText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
  },
  listContent: {
    paddingHorizontal: 16,
  },
  // Apple-style clean event item
  simpleEventItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 0,
    marginBottom: 2,
  },
  dateLabel: {
    fontSize: 13,
    fontWeight: '500',
    width: 50,
    textAlign: 'left',
  },
  eventTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  eventText: {
    fontSize: 15,
    fontWeight: '400',
    lineHeight: 20,
    marginBottom: 1,
  },
  emptyContainer: {
    marginHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
    opacity: 0.6,
  },
});

export default EventsList;
