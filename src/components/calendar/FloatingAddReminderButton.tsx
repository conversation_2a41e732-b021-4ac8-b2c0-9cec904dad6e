import React, { useRef } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  Platform,
} from 'react-native';
import { Plus } from 'lucide-react-native';
import { useDarkMode } from '@/store/settings-store';
import colors from '@/constants/colors';

interface FloatingAddReminderButtonProps {
  onPress: () => void;
}

const FloatingAddReminderButton: React.FC<FloatingAddReminderButtonProps> = ({ onPress }) => {
  const isDarkMode = useDarkMode();
  const theme = isDarkMode ? colors.dark : colors.light;

  // Simple scale animation for press feedback
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Simple press animations
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.9,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 300,
      friction: 10,
      useNativeDriver: true,
    }).start();
  };



  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: theme.primary,
            shadowColor: theme.primary,
          },
        ]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.8}
      >
        <Plus
          size={24}
          color={theme.buttonText}
          strokeWidth={2.5}
        />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 6,
      },
    }),
  },
});

export default FloatingAddReminderButton;
